// Social Network V2: Clean, LinkedIn-style connections component
// Purpose: Social feed visibility + Activity notifications (no messaging)

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  UserPlus, 
  UserCheck, 
  UserX, 
  Clock, 
  Send,
  Trash2,
  RefreshCw,
  TrendingUp
} from 'lucide-react';
import { useSocialNetworkV2 } from '@/hooks/social/useSocialNetworkV2';
import { getCloudflareImageUrl } from '@/lib/cloudflare';

interface SocialNetworkV2Props {
  defaultTab?: string;
  showStats?: boolean;
}

export function SocialNetworkV2({ defaultTab = 'connections', showStats = true }: SocialNetworkV2Props) {
  const {
    connections,
    connectionRequests,
    sentRequests,
    suggestedConnections,
    stats,
    isLoading,
    isRefreshing,
    sendConnectionRequest,
    acceptConnectionRequest,
    declineConnectionRequest,
    cancelConnectionRequest,
    removeConnection,
    loadSuggestedConnections,
    refreshAll,
    getDisplayName,
    getRoleDisplay
  } = useSocialNetworkV2();

  const [activeTab, setActiveTab] = useState(defaultTab);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);

  // Load suggestions when switching to suggestions tab
  useEffect(() => {
    if (activeTab === 'suggestions') {
      setLoadingSuggestions(true);
      loadSuggestedConnections(10).finally(() => setLoadingSuggestions(false));
    }
  }, [activeTab, loadSuggestedConnections]);

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Stats Cards */}
      {showStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="flex items-center p-6">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.total_connections}</p>
                <p className="text-sm text-muted-foreground">Connections</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="flex items-center p-6">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.pending_requests}</p>
                <p className="text-sm text-muted-foreground">Pending Requests</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="flex items-center p-6">
              <Send className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.sent_requests}</p>
                <p className="text-sm text-muted-foreground">Sent Requests</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Social Network
            </CardTitle>
            <CardDescription>
              Manage your professional connections and discover new opportunities
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refreshAll}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="connections" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Connections
                {stats.total_connections > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {stats.total_connections}
                  </Badge>
                )}
              </TabsTrigger>
              
              <TabsTrigger value="requests" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Requests
                {stats.pending_requests > 0 && (
                  <Badge variant="destructive" className="ml-1">
                    {stats.pending_requests}
                  </Badge>
                )}
              </TabsTrigger>
              
              <TabsTrigger value="sent" className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Sent
                {stats.sent_requests > 0 && (
                  <Badge variant="outline" className="ml-1">
                    {stats.sent_requests}
                  </Badge>
                )}
              </TabsTrigger>
              
              <TabsTrigger value="suggestions" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Suggestions
                {suggestedConnections.length > 0 && (
                  <Badge variant="outline" className="ml-1">
                    {suggestedConnections.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Connections Tab */}
            <TabsContent value="connections" className="mt-6">
              <div className="space-y-4">
                {connections.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No connections yet</h3>
                    <p>Start building your professional network by connecting with colleagues and peers.</p>
                  </div>
                ) : (
                  connections.map((connection) => (
                    <ConnectionCard
                      key={connection.connection_id}
                      connection={connection}
                      onRemove={() => removeConnection(connection.connection_id)}
                      getDisplayName={getDisplayName}
                      getRoleDisplay={getRoleDisplay}
                      isLoading={isLoading}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            {/* Requests Tab */}
            <TabsContent value="requests" className="mt-6">
              <div className="space-y-4">
                {connectionRequests.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No pending requests</h3>
                    <p>Connection requests from other users will appear here.</p>
                  </div>
                ) : (
                  connectionRequests.map((request) => (
                    <RequestCard
                      key={request.connection_id}
                      request={request}
                      onAccept={() => acceptConnectionRequest(request.connection_id)}
                      onDecline={() => declineConnectionRequest(request.connection_id)}
                      getDisplayName={getDisplayName}
                      getRoleDisplay={getRoleDisplay}
                      isLoading={isLoading}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            {/* Sent Tab */}
            <TabsContent value="sent" className="mt-6">
              <div className="space-y-4">
                {sentRequests.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <Send className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No sent requests</h3>
                    <p>Connection requests you send will appear here until they're accepted or declined.</p>
                  </div>
                ) : (
                  sentRequests.map((request) => (
                    <SentRequestCard
                      key={request.connection_id}
                      request={request}
                      onCancel={() => cancelConnectionRequest(request.connection_id)}
                      getDisplayName={getDisplayName}
                      getRoleDisplay={getRoleDisplay}
                      isLoading={isLoading}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            {/* Suggestions Tab */}
            <TabsContent value="suggestions" className="mt-6">
              <div className="space-y-4">
                {loadingSuggestions ? (
                  <div className="text-center py-12">
                    <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-muted-foreground" />
                    <p className="text-muted-foreground">Loading suggestions...</p>
                  </div>
                ) : suggestedConnections.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No suggestions available</h3>
                    <p>We'll suggest new connections as more users join the platform.</p>
                  </div>
                ) : (
                  suggestedConnections.map((suggestion) => (
                    <SuggestionCard
                      key={suggestion.user_id}
                      suggestion={suggestion}
                      onConnect={() => sendConnectionRequest(suggestion.user_id)}
                      getDisplayName={getDisplayName}
                      getRoleDisplay={getRoleDisplay}
                      isLoading={isLoading}
                    />
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// =====================================================
// CARD COMPONENTS
// =====================================================

interface ConnectionCardProps {
  connection: any;
  onRemove: () => void;
  getDisplayName: (firstName: string, lastName: string) => string;
  getRoleDisplay: (title: string | null, organization: string | null) => string;
  isLoading: boolean;
}

function ConnectionCard({ connection, onRemove, getDisplayName, getRoleDisplay, isLoading }: ConnectionCardProps) {
  const displayName = getDisplayName(connection.first_name, connection.last_name);
  const role = getRoleDisplay(connection.title, connection.organization);
  const avatarUrl = connection.avatar_url ? getCloudflareImageUrl(connection.avatar_url) : null;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12">
          <AvatarImage src={avatarUrl || undefined} alt={displayName} />
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
            {displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h4 className="font-medium">{displayName}</h4>
          <p className="text-sm text-muted-foreground">{role}</p>
          <p className="text-xs text-muted-foreground">
            Connected {new Date(connection.connected_at).toLocaleDateString()}
          </p>
        </div>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={onRemove}
        disabled={isLoading}
        className="text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Remove
      </Button>
    </div>
  );
}

interface RequestCardProps {
  request: any;
  onAccept: () => void;
  onDecline: () => void;
  getDisplayName: (firstName: string, lastName: string) => string;
  getRoleDisplay: (title: string | null, organization: string | null) => string;
  isLoading: boolean;
}

function RequestCard({ request, onAccept, onDecline, getDisplayName, getRoleDisplay, isLoading }: RequestCardProps) {
  const displayName = getDisplayName(request.first_name, request.last_name);
  const role = getRoleDisplay(request.title, request.organization);
  const avatarUrl = request.avatar_url ? getCloudflareImageUrl(request.avatar_url) : null;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12">
          <AvatarImage src={avatarUrl || undefined} alt={displayName} />
          <AvatarFallback className="bg-gradient-to-br from-orange-500 to-red-600 text-white">
            {displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h4 className="font-medium">{displayName}</h4>
          <p className="text-sm text-muted-foreground">{role}</p>
          <p className="text-xs text-muted-foreground">
            Sent {new Date(request.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>
      <div className="flex space-x-2">
        <Button
          size="sm"
          onClick={onAccept}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          <UserCheck className="h-4 w-4 mr-2" />
          Accept
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onDecline}
          disabled={isLoading}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <UserX className="h-4 w-4 mr-2" />
          Decline
        </Button>
      </div>
    </div>
  );
}

interface SentRequestCardProps {
  request: any;
  onCancel: () => void;
  getDisplayName: (firstName: string, lastName: string) => string;
  getRoleDisplay: (title: string | null, organization: string | null) => string;
  isLoading: boolean;
}

function SentRequestCard({ request, onCancel, getDisplayName, getRoleDisplay, isLoading }: SentRequestCardProps) {
  const displayName = getDisplayName(request.first_name, request.last_name);
  const role = getRoleDisplay(request.title, request.organization);
  const avatarUrl = request.avatar_url ? getCloudflareImageUrl(request.avatar_url) : null;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12">
          <AvatarImage src={avatarUrl || undefined} alt={displayName} />
          <AvatarFallback className="bg-gradient-to-br from-yellow-500 to-orange-600 text-white">
            {displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h4 className="font-medium">{displayName}</h4>
          <p className="text-sm text-muted-foreground">{role}</p>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Badge>
            <p className="text-xs text-muted-foreground">
              Sent {new Date(request.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={onCancel}
        disabled={isLoading}
        className="text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        <UserX className="h-4 w-4 mr-2" />
        Cancel
      </Button>
    </div>
  );
}

interface SuggestionCardProps {
  suggestion: any;
  onConnect: () => void;
  getDisplayName: (firstName: string, lastName: string) => string;
  getRoleDisplay: (title: string | null, organization: string | null) => string;
  isLoading: boolean;
}

function SuggestionCard({ suggestion, onConnect, getDisplayName, getRoleDisplay, isLoading }: SuggestionCardProps) {
  const displayName = getDisplayName(suggestion.first_name, suggestion.last_name);
  const role = getRoleDisplay(suggestion.title, suggestion.organization);
  const avatarUrl = suggestion.avatar_url ? getCloudflareImageUrl(suggestion.avatar_url) : null;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12">
          <AvatarImage src={avatarUrl || undefined} alt={displayName} />
          <AvatarFallback className="bg-gradient-to-br from-green-500 to-blue-600 text-white">
            {displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h4 className="font-medium">{displayName}</h4>
          <p className="text-sm text-muted-foreground">{role}</p>
          {suggestion.mutual_connections > 0 && (
            <p className="text-xs text-blue-600">
              {suggestion.mutual_connections} mutual connection{suggestion.mutual_connections !== 1 ? 's' : ''}
            </p>
          )}
        </div>
      </div>
      <Button
        size="sm"
        onClick={onConnect}
        disabled={isLoading}
        className="bg-blue-600 hover:bg-blue-700"
      >
        <UserPlus className="h-4 w-4 mr-2" />
        Connect
      </Button>
    </div>
  );
}
