import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Businesses from "./pages/Businesses";
import Professionals from "./pages/Professionals";
import Jobs from "./pages/Jobs";
import Education from "./pages/Education";
import NotFound from "./pages/NotFound";
import BusinessProfile from "./pages/profiles/BusinessProfile";
import EventProfile from "./pages/profiles/EventProfile";
import ProfessionalProfile from "./pages/profiles/ProfessionalProfile";
import JobProfile from "./pages/profiles/JobProfile";
import { EducationProfile } from "@/features/Education";
import SocialFeed from "./pages/SocialFeed";
import SocialPostDetail from "./pages/SocialPostDetail";
import UserDashboard from "./pages/UserDashboard";
import Subscription from "./pages/Subscription";
import FundingFinder from "./pages/FundingFinder";
import FundingProfile from "./components/funding/FundingProfile";
import Auth from "./pages/Auth";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import BusinessEditor from "./pages/BusinessEditor";
import TestProfiles from "./pages/TestProfiles";
import TestConnectionsPage from "./pages/TestConnectionsPage";
import ConnectionsPage from "./pages/ConnectionsPage";

// Event feature imports
import { eventRoutes } from '@/features/events/routes'; // Added import for centralized event routes

const queryClient = new QueryClient();

// Component that renders routes only after auth state is determined
const AppRoutes = () => {
  const { isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/businesses" element={<Businesses />} />
        <Route path="/businesses/:id" element={<BusinessProfile />} />
        <Route path="/businesses/new" element={
          <ProtectedRoute>
            <BusinessEditor />
          </ProtectedRoute>
        } />
        <Route path="/businesses/:id/edit" element={
          <ProtectedRoute>
            <BusinessEditor />
          </ProtectedRoute>
        } />

        {/* Correctly integrate eventRoutes by spreading them */}
        {eventRoutes.map((route, index) => (
          <Route key={index} path={route.path} element={route.element}>
            {route.children && route.children.map((childRoute, childIndex) => (
              <Route key={childIndex} index={childRoute.index} path={childRoute.path} element={childRoute.element} />
            ))}
          </Route>
        ))}                <Route path="/members" element={<Professionals />} />
        <Route path="/members/:id" element={<ProfessionalProfile />} />
        <Route path="/test-profiles" element={<TestProfiles />} />
        <Route path="/jobs" element={<Jobs />} />
        <Route path="/jobs/:id" element={<JobProfile />} />
        <Route path="/education" element={<Education />} />
        <Route path="/education/:id" element={<EducationProfile />} />
        <Route path="/social" element={<SocialFeed />} />
        <Route path="/social/post/:id" element={<SocialPostDetail />} />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <UserDashboard />
          </ProtectedRoute>
        } />                <Route path="/subscription" element={<Subscription />} />                <Route path="/funding" element={<FundingFinder />} />
        <Route path="/funding/:id" element={<FundingProfile />} />
        <Route path="/auth" element={<Auth />} />
        <Route path="/test-connections" element={<TestConnectionsPage />} />
        <Route path="/connections" element={<ConnectionsPage />} />

        {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <AppRoutes />
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
